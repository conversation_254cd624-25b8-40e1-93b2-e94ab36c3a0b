["test_document_types_endpoints.py::TestDocumentTypesAPI::test_create_document_type_duplicate_slug", "test_document_types_endpoints.py::TestDocumentTypesAPI::test_create_document_type_forbidden_non_admin", "test_document_types_endpoints.py::TestDocumentTypesAPI::test_create_document_type_invalid_data", "test_document_types_endpoints.py::TestDocumentTypesAPI::test_create_document_type_success_admin", "test_document_types_endpoints.py::TestDocumentTypesAPI::test_delete_document_type_success", "test_document_types_endpoints.py::TestDocumentTypesAPI::test_get_ai_agents_success", "test_document_types_endpoints.py::TestDocumentTypesAPI::test_get_by_category_success", "test_document_types_endpoints.py::TestDocumentTypesAPI::test_get_by_industry_success", "test_document_types_endpoints.py::TestDocumentTypesAPI::test_get_document_type_not_found", "test_document_types_endpoints.py::TestDocumentTypesAPI::test_get_document_type_success", "test_document_types_endpoints.py::TestDocumentTypesAPI::test_get_form_schema_success", "test_document_types_endpoints.py::TestDocumentTypesAPI::test_get_refinement_options_success", "test_document_types_endpoints.py::TestDocumentTypesAPI::test_get_template_structure_success", "test_document_types_endpoints.py::TestDocumentTypesAPI::test_list_document_types_success", "test_document_types_endpoints.py::TestDocumentTypesAPI::test_list_document_types_with_filters", "test_document_types_endpoints.py::TestDocumentTypesAPI::test_update_document_type_forbidden_non_admin", "test_document_types_endpoints.py::TestDocumentTypesAPI::test_update_document_type_success"]