#!/usr/bin/env python3
"""
Create database tables using SQLAlchemy models.
This script will create all the necessary tables for the complete PRD Generator system,
including documents, document types, users, tenants, and all related functionality.
"""

import os
import sys
from dotenv import load_dotenv
from sqlalchemy import create_engine, text
from sqlalchemy.orm import sessionmaker

# Load environment variables
load_dotenv()

# Add the app directory to the Python path
sys.path.append(os.path.join(os.path.dirname(__file__), 'app'))

def import_all_models():
    """Import all models to ensure they are registered with SQLAlchemy."""
    try:
        # Core models
        from app.models.user import User
        from app.models.tenant import Tenant

        # Document-related models
        from app.models.document import (
            Document, DocumentVersion, DocumentGenerationSession, DocumentRefinementJob,
            DocumentWorkflowState, WorkflowTemplate, DocumentWorkflowAssignment, DocumentWorkflowComment
        )
        from app.models.document_type import DocumentType

        # AI and Agent models
        from app.models.agent import AIAgent, AIPrompt, AgentJob

        # Analytics models
        from app.models.analytics import (
            AnalyticsMetric, DocumentAnalytics, TeamPerformanceMetrics,
            AIEffectivenessMetrics, ExportUsageMetrics, FormCompletionMetrics
        )

        # Export models
        from app.models.export import ExportJob, ExportFile, ExportHistory, ExportIntegration

        # Form and template models
        from app.models.form_schema import FormSchema, FormQuestion, FormValidationLog
        from app.models.template import DocumentTemplate, ComponentLibrary, SectionLibrary, ComponentUsageLog, SectionUsageLog

        # Learning models
        from app.models.learning import LearningProfile, AIFeedback, LearnedPattern, LearningSession

        print("✅ All models imported successfully!")
        return True

    except ImportError as e:
        print(f"⚠️  Could not import some models: {str(e)}")
        print("   This might be expected if some models don't exist yet.")
        return True
    except Exception as e:
        print(f"❌ Error importing models: {str(e)}")
        return False

def create_tables():
    """Create all database tables."""
    try:
        # Import models first
        if not import_all_models():
            return False

        # Import after adding to path
        from app.core.database import Base, engine

        print("🔧 Creating database tables...")
        print("=" * 50)

        # Create all tables
        Base.metadata.create_all(bind=engine)

        print("✅ Database tables created successfully!")

        # Create a session to insert default data
        SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
        db = SessionLocal()

        try:
            # Check if default tenant exists
            result = db.execute(text("SELECT COUNT(*) FROM tenants WHERE id = '00000000-0000-0000-0000-000000000000'"))
            count = result.scalar()

            if count == 0:
                # Insert default tenant
                db.execute(text("""
                    INSERT INTO tenants (id, name, slug, plan, settings, theme, created_at, updated_at)
                    VALUES (
                        '00000000-0000-0000-0000-000000000000',
                        'Default Tenant',
                        'default',
                        'free',
                        '{}',
                        'default',
                        NOW(),
                        NOW()
                    )
                """))
                db.commit()
                print("✅ Default tenant created!")
            else:
                print("ℹ️  Default tenant already exists")

        except Exception as e:
            print(f"⚠️  Could not create default tenant: {str(e)}")
            db.rollback()
        finally:
            db.close()

        return True

    except Exception as e:
        print(f"❌ Error creating tables: {str(e)}")
        return False

def verify_tables():
    """Verify that all expected tables were created."""
    try:
        from app.core.database import engine

        print("\n📋 Verifying created tables...")

        # Expected core tables for documents CRUD API
        expected_tables = [
            'users', 'tenants', 'documents', 'document_versions', 'document_types',
            'document_generation_sessions', 'document_refinement_jobs',
            'document_workflow_states', 'workflow_templates', 'document_workflow_assignments', 'document_workflow_comments',
            'ai_agents', 'ai_prompts', 'agent_jobs',
            'export_jobs', 'export_files', 'export_history', 'export_integrations',
            'form_schemas', 'form_questions', 'form_validation_logs',
            'document_templates', 'component_library', 'section_library',
            'learning_profiles', 'ai_feedback', 'learned_patterns', 'learning_sessions',
            'analytics_metrics', 'document_analytics', 'team_performance_metrics',
            'ai_effectiveness_metrics', 'export_usage_metrics', 'form_completion_metrics'
        ]

        with engine.connect() as conn:
            result = conn.execute(text("""
                SELECT table_name
                FROM information_schema.tables
                WHERE table_schema = 'public'
                ORDER BY table_name
            """))

            actual_tables = [row[0] for row in result]

            print("✅ Created tables:")
            for table in sorted(actual_tables):
                status = "✓" if table in expected_tables else "?"
                print(f"   {status} {table}")

            # Check for missing critical tables
            missing_critical = []
            critical_tables = ['users', 'tenants', 'documents', 'document_types', 'document_versions']

            for table in critical_tables:
                if table not in actual_tables:
                    missing_critical.append(table)

            if missing_critical:
                print(f"\n❌ Missing critical tables: {', '.join(missing_critical)}")
                return False
            else:
                print(f"\n✅ All critical tables created successfully!")
                print(f"   Total tables: {len(actual_tables)}")
                return True

    except Exception as e:
        print(f"❌ Error verifying tables: {str(e)}")
        return False

def check_table_structure():
    """Check the structure of key tables."""
    try:
        from app.core.database import engine

        print("\n🔍 Checking key table structures...")

        # Check documents table structure
        with engine.connect() as conn:
            result = conn.execute(text("""
                SELECT column_name, data_type, is_nullable
                FROM information_schema.columns
                WHERE table_name = 'documents'
                    AND table_schema = 'public'
                ORDER BY ordinal_position
            """))

            columns = list(result)
            if columns:
                print("✅ Documents table columns:")
                for col_name, data_type, nullable in columns:
                    print(f"   - {col_name}: {data_type} ({'NULL' if nullable == 'YES' else 'NOT NULL'})")
            else:
                print("⚠️  Documents table not found or has no columns")

        # Check document_types table structure
        with engine.connect() as conn:
            result = conn.execute(text("""
                SELECT column_name, data_type, is_nullable
                FROM information_schema.columns
                WHERE table_name = 'document_types'
                    AND table_schema = 'public'
                ORDER BY ordinal_position
            """))

            columns = list(result)
            if columns:
                print("\n✅ Document Types table columns:")
                for col_name, data_type, nullable in columns:
                    print(f"   - {col_name}: {data_type} ({'NULL' if nullable == 'YES' else 'NOT NULL'})")
            else:
                print("⚠️  Document Types table not found or has no columns")

        return True

    except Exception as e:
        print(f"❌ Error checking table structure: {str(e)}")
        return False

def test_connection():
    """Test database connection."""
    try:
        from app.core.database import engine

        print("🔗 Testing database connection...")
        with engine.connect() as conn:
            result = conn.execute(text("SELECT 1"))
            if result.scalar() == 1:
                print("✅ Database connection successful!")
                return True
            else:
                print("❌ Database connection test failed!")
                return False

    except Exception as e:
        print(f"❌ Database connection failed: {str(e)}")
        return False

def main():
    """Main function."""
    print("🚀 PRD Generator Database Setup Script")
    print("=" * 50)

    # Test connection first
    if not test_connection():
        print("\n❌ Cannot proceed without database connection.")
        print("Please check your DATABASE_URL in .env file.")
        return

    # Create tables
    if create_tables():
        # Verify tables were created
        if verify_tables():
            # Check table structures
            check_table_structure()

            print("\n🎉 Database setup completed successfully!")
            print("✅ All tables created and verified")
            print("✅ Documents CRUD API is ready to use")
            print("\nNext steps:")
            print("1. Start the FastAPI server: uvicorn app.main:app --reload")
            print("2. Test the API endpoints at http://localhost:8000/docs")
            print("3. Create document types and start generating documents")
        else:
            print("\n⚠️  Database setup completed with warnings.")
            print("Some tables may be missing. Check the output above.")
    else:
        print("\n❌ Database setup failed!")
        print("Please check the error messages above.")

if __name__ == "__main__":
    main()
