"""
Database migration utilities for the PRD Generator system.
Provides functions for checking existing tables, creating missing tables, and handling data migrations.
"""

import os
import sys
from typing import List, Dict, Any, Optional
from sqlalchemy import create_engine, text, inspect
from sqlalchemy.orm import sessionmaker
from sqlalchemy.exc import SQLAlchemyError
import logging

from app.core.config import settings
from app.core.database import Base, engine

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class DatabaseMigrator:
    """Database migration utility class."""
    
    def __init__(self):
        self.engine = engine
        self.inspector = inspect(self.engine)
        
    def get_existing_tables(self) -> List[str]:
        """Get list of existing tables in the database."""
        try:
            return self.inspector.get_table_names()
        except Exception as e:
            logger.error(f"Error getting existing tables: {str(e)}")
            return []
    
    def table_exists(self, table_name: str) -> bool:
        """Check if a specific table exists."""
        return table_name in self.get_existing_tables()
    
    def get_table_columns(self, table_name: str) -> List[Dict[str, Any]]:
        """Get column information for a specific table."""
        try:
            if not self.table_exists(table_name):
                return []
            
            columns = self.inspector.get_columns(table_name)
            return columns
        except Exception as e:
            logger.error(f"Error getting columns for table {table_name}: {str(e)}")
            return []
    
    def create_missing_tables(self, specific_models: Optional[List] = None) -> bool:
        """Create missing tables based on SQLAlchemy models."""
        try:
            if specific_models:
                # Create only specific model tables
                for model in specific_models:
                    model.__table__.create(self.engine, checkfirst=True)
                    logger.info(f"Created table for model: {model.__name__}")
            else:
                # Create all tables
                Base.metadata.create_all(bind=self.engine)
                logger.info("Created all missing tables")
            
            return True
        except Exception as e:
            logger.error(f"Error creating tables: {str(e)}")
            return False
    
    def check_required_tables(self) -> Dict[str, bool]:
        """Check if all required tables for documents CRUD API exist."""
        required_tables = {
            'users': False,
            'tenants': False,
            'documents': False,
            'document_types': False,
            'document_versions': False,
            'document_generation_sessions': False,
            'document_refinement_jobs': False,
        }
        
        existing_tables = self.get_existing_tables()
        
        for table in required_tables:
            required_tables[table] = table in existing_tables
        
        return required_tables
    
    def verify_document_api_readiness(self) -> bool:
        """Verify that all required tables for documents CRUD API exist."""
        required_tables = self.check_required_tables()
        missing_tables = [table for table, exists in required_tables.items() if not exists]
        
        if missing_tables:
            logger.warning(f"Missing required tables: {', '.join(missing_tables)}")
            return False
        
        logger.info("All required tables for documents CRUD API are present")
        return True
    
    def create_default_data(self) -> bool:
        """Create default data required for the system to function."""
        try:
            SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=self.engine)
            db = SessionLocal()
            
            try:
                # Check if default tenant exists
                result = db.execute(text("SELECT COUNT(*) FROM tenants WHERE slug = 'default'"))
                count = result.scalar()
                
                if count == 0:
                    # Insert default tenant
                    db.execute(text("""
                        INSERT INTO tenants (id, name, slug, plan, settings, theme, created_at, updated_at) 
                        VALUES (
                            '00000000-0000-0000-0000-000000000000', 
                            'Default Tenant', 
                            'default', 
                            'free',
                            '{}',
                            'default',
                            NOW(),
                            NOW()
                        )
                    """))
                    db.commit()
                    logger.info("Created default tenant")
                else:
                    logger.info("Default tenant already exists")
                
                # Create default document types if they don't exist
                self._create_default_document_types(db)
                
                return True
                
            except Exception as e:
                logger.error(f"Error creating default data: {str(e)}")
                db.rollback()
                return False
            finally:
                db.close()
                
        except Exception as e:
            logger.error(f"Error setting up database session: {str(e)}")
            return False
    
    def _create_default_document_types(self, db) -> None:
        """Create default document types."""
        try:
            # Check if any document types exist
            result = db.execute(text("SELECT COUNT(*) FROM document_types"))
            count = result.scalar()
            
            if count == 0:
                # Insert default PRD document type
                db.execute(text("""
                    INSERT INTO document_types (
                        id, name, slug, description, category, 
                        form_schema, template_structure, is_system_default, 
                        is_active, version, created_at, updated_at
                    ) VALUES (
                        '11111111-1111-1111-1111-111111111111',
                        'Product Requirements Document',
                        'prd',
                        'Comprehensive product requirements document template',
                        'product',
                        '{"sections": [{"name": "overview", "required": true}, {"name": "features", "required": true}]}',
                        '{"sections": ["Overview", "Problem Statement", "Solution", "Features", "Requirements"]}',
                        true,
                        true,
                        '1.0',
                        NOW(),
                        NOW()
                    )
                """))
                
                # Insert default API Documentation type
                db.execute(text("""
                    INSERT INTO document_types (
                        id, name, slug, description, category, 
                        form_schema, template_structure, is_system_default, 
                        is_active, version, created_at, updated_at
                    ) VALUES (
                        '22222222-2222-2222-2222-222222222222',
                        'API Documentation',
                        'api-doc',
                        'Technical API documentation template',
                        'technical',
                        '{"sections": [{"name": "endpoints", "required": true}, {"name": "authentication", "required": true}]}',
                        '{"sections": ["Introduction", "Authentication", "Endpoints", "Examples", "Error Codes"]}',
                        true,
                        true,
                        '1.0',
                        NOW(),
                        NOW()
                    )
                """))
                
                db.commit()
                logger.info("Created default document types")
            else:
                logger.info("Document types already exist")
                
        except Exception as e:
            logger.error(f"Error creating default document types: {str(e)}")
            db.rollback()
    
    def run_migration(self) -> bool:
        """Run complete migration process."""
        logger.info("Starting database migration...")
        
        # Step 1: Create missing tables
        if not self.create_missing_tables():
            logger.error("Failed to create missing tables")
            return False
        
        # Step 2: Verify required tables exist
        if not self.verify_document_api_readiness():
            logger.error("Documents CRUD API requirements not met")
            return False
        
        # Step 3: Create default data
        if not self.create_default_data():
            logger.error("Failed to create default data")
            return False
        
        logger.info("Database migration completed successfully!")
        return True


def run_migration() -> bool:
    """Convenience function to run migration."""
    migrator = DatabaseMigrator()
    return migrator.run_migration()


def check_database_status() -> Dict[str, Any]:
    """Check the current status of the database."""
    migrator = DatabaseMigrator()
    
    status = {
        'connection': False,
        'tables_exist': {},
        'required_tables_ready': False,
        'total_tables': 0
    }
    
    try:
        # Test connection
        with migrator.engine.connect() as conn:
            conn.execute(text("SELECT 1"))
            status['connection'] = True
        
        # Check tables
        status['tables_exist'] = migrator.check_required_tables()
        status['required_tables_ready'] = migrator.verify_document_api_readiness()
        status['total_tables'] = len(migrator.get_existing_tables())
        
    except Exception as e:
        logger.error(f"Error checking database status: {str(e)}")
    
    return status
