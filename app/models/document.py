from sqlalchemy import Column, String, DateTime, Text, JSON, Integer, ForeignKey
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
import uuid
from app.core.database import Base

class Document(Base):
    __tablename__ = "documents"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    title = Column(String, nullable=False)
    content = Column(Text, nullable=False)
    
    # Document type reference
    document_type_id = Column(UUID(as_uuid=True), ForeignKey("document_types.id"), nullable=False)
    
    # Form data used to generate the document
    form_data = Column(JSON, nullable=False)
    
    # Document metadata
    status = Column(String, default="draft")  # draft, review, approved, published, archived
    version = Column(String, default="1.0")
    version_number = Column(Integer, default=1)
    
    # Ownership and tenant
    user_id = Column(UUID(as_uuid=True), nullable=False)
    tenant_id = Column(UUID(as_uuid=True), nullable=False)
    
    # Collaboration
    shared_with = Column(JSON, default=[])  # List of user IDs with access
    permissions = Column(JSON, default={
        "can_view": [],
        "can_edit": [],
        "can_comment": []
    })
    
    # Analytics
    view_count = Column(Integer, default=0)
    last_viewed_at = Column(DateTime(timezone=True), nullable=True)
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    published_at = Column(DateTime(timezone=True), nullable=True)

class DocumentVersion(Base):
    __tablename__ = "document_versions"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    document_id = Column(UUID(as_uuid=True), ForeignKey("documents.id"), nullable=False)
    
    # Version details
    version = Column(String, nullable=False)
    version_number = Column(Integer, nullable=False)
    
    # Content snapshot
    title = Column(String, nullable=False)
    content = Column(Text, nullable=False)
    form_data = Column(JSON, nullable=False)
    status = Column(String, nullable=False)
    
    # Change metadata
    change_summary = Column(String, nullable=True)
    created_by = Column(UUID(as_uuid=True), nullable=False)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    
    # Relationships
    document = relationship("Document", backref="versions")

class DocumentGenerationSession(Base):
    __tablename__ = "document_generation_sessions"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    document_id = Column(UUID(as_uuid=True), ForeignKey("documents.id"), nullable=False)
    user_id = Column(UUID(as_uuid=True), nullable=False)
    tenant_id = Column(UUID(as_uuid=True), nullable=False)

    # Session data
    session_type = Column(String, nullable=False)  # initial, followup, regeneration
    status = Column(String, default="pending")  # pending, processing, completed, failed
    progress = Column(Integer, default=0)

    # Generation context
    original_form_data = Column(JSON, nullable=False)
    followup_questions = Column(JSON, default=[])
    followup_answers = Column(JSON, default={})
    generation_instructions = Column(Text, nullable=True)

    # Results
    generated_content = Column(Text, nullable=True)
    error_message = Column(Text, nullable=True)

    # Timestamps
    started_at = Column(DateTime(timezone=True), server_default=func.now())
    completed_at = Column(DateTime(timezone=True), nullable=True)

    # Relationships
    document = relationship("Document", backref="generation_sessions")

class DocumentRefinementJob(Base):
    __tablename__ = "document_refinement_jobs"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    document_id = Column(UUID(as_uuid=True), ForeignKey("documents.id"), nullable=False)
    user_id = Column(UUID(as_uuid=True), nullable=False)
    tenant_id = Column(UUID(as_uuid=True), nullable=False)

    # Job details
    job_type = Column(String, nullable=False)  # section, component, custom, suggestions
    status = Column(String, default="pending")  # pending, processing, completed, failed, cancelled
    progress = Column(Integer, default=0)

    # Refinement context
    target = Column(String, nullable=False)  # section name, component id, or "document"
    refinement_type = Column(String, nullable=False)
    instructions = Column(Text, nullable=True)
    parameters = Column(JSON, default={})

    # Original and refined content
    original_content = Column(Text, nullable=True)
    refined_content = Column(Text, nullable=True)

    # Job metadata
    estimated_completion = Column(DateTime(timezone=True), nullable=True)
    error_message = Column(Text, nullable=True)

    # Timestamps
    started_at = Column(DateTime(timezone=True), server_default=func.now())
    completed_at = Column(DateTime(timezone=True), nullable=True)
    cancelled_at = Column(DateTime(timezone=True), nullable=True)

    # Relationships
    document = relationship("Document", backref="refinement_jobs")


class DocumentWorkflowState(Base):
    """Track document workflow states and transitions"""
    __tablename__ = "document_workflow_states"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    document_id = Column(UUID(as_uuid=True), ForeignKey("documents.id"), nullable=False)
    workflow_template_id = Column(UUID(as_uuid=True), ForeignKey("workflow_templates.id"), nullable=True)

    # Current state
    current_state = Column(String, nullable=False)  # draft, review, approval, published, rejected
    previous_state = Column(String, nullable=True)

    # Assignment details
    assigned_to = Column(UUID(as_uuid=True), nullable=True)  # User ID
    assigned_role = Column(String, nullable=True)  # reviewer, approver, editor
    due_date = Column(DateTime(timezone=True), nullable=True)

    # Workflow context
    workflow_data = Column(JSON, default={})  # Custom workflow data
    priority = Column(String, default="normal")  # low, normal, high, urgent

    # Metadata
    user_id = Column(UUID(as_uuid=True), nullable=False)
    tenant_id = Column(UUID(as_uuid=True), nullable=False)

    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    # Relationships
    document = relationship("Document", backref="workflow_states")


class WorkflowTemplate(Base):
    """Define reusable workflow templates"""
    __tablename__ = "workflow_templates"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    name = Column(String, nullable=False)
    description = Column(Text, nullable=True)

    # Template configuration
    workflow_steps = Column(JSON, nullable=False)  # Ordered list of workflow steps
    default_assignments = Column(JSON, default={})  # Default role assignments
    automation_rules = Column(JSON, default={})  # Automated transition rules

    # Applicability
    document_types = Column(JSON, default=[])  # Document type IDs this applies to
    conditions = Column(JSON, default={})  # Conditions for auto-applying this template

    # Status
    is_active = Column(Boolean, default=True)
    is_default = Column(Boolean, default=False)

    # Ownership
    created_by = Column(UUID(as_uuid=True), nullable=False)
    tenant_id = Column(UUID(as_uuid=True), nullable=False)

    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())


class DocumentWorkflowAssignment(Base):
    """Track workflow assignments and tasks"""
    __tablename__ = "document_workflow_assignments"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    document_id = Column(UUID(as_uuid=True), ForeignKey("documents.id"), nullable=False)
    workflow_state_id = Column(UUID(as_uuid=True), ForeignKey("document_workflow_states.id"), nullable=False)

    # Assignment details
    assigned_to = Column(UUID(as_uuid=True), nullable=False)  # User ID
    assigned_by = Column(UUID(as_uuid=True), nullable=False)  # User ID who made assignment
    assignment_type = Column(String, nullable=False)  # review, approve, edit, comment

    # Task details
    task_description = Column(Text, nullable=True)
    instructions = Column(Text, nullable=True)
    due_date = Column(DateTime(timezone=True), nullable=True)
    priority = Column(String, default="normal")

    # Status
    status = Column(String, default="pending")  # pending, in_progress, completed, declined, overdue
    completion_notes = Column(Text, nullable=True)

    # Metadata
    tenant_id = Column(UUID(as_uuid=True), nullable=False)

    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    completed_at = Column(DateTime(timezone=True), nullable=True)

    # Relationships
    document = relationship("Document", backref="workflow_assignments")
    workflow_state = relationship("DocumentWorkflowState", backref="assignments")


class DocumentWorkflowComment(Base):
    """Comments and feedback during workflow processes"""
    __tablename__ = "document_workflow_comments"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    document_id = Column(UUID(as_uuid=True), ForeignKey("documents.id"), nullable=False)
    workflow_state_id = Column(UUID(as_uuid=True), ForeignKey("document_workflow_states.id"), nullable=True)
    assignment_id = Column(UUID(as_uuid=True), ForeignKey("document_workflow_assignments.id"), nullable=True)

    # Comment details
    comment_text = Column(Text, nullable=False)
    comment_type = Column(String, default="general")  # general, suggestion, issue, approval, rejection

    # Context
    section_reference = Column(String, nullable=True)  # Which section this comment refers to
    line_reference = Column(Integer, nullable=True)  # Line number if applicable
    context_data = Column(JSON, default={})  # Additional context

    # Status
    is_resolved = Column(Boolean, default=False)
    resolution_notes = Column(Text, nullable=True)
    resolved_by = Column(UUID(as_uuid=True), nullable=True)
    resolved_at = Column(DateTime(timezone=True), nullable=True)

    # Metadata
    created_by = Column(UUID(as_uuid=True), nullable=False)
    tenant_id = Column(UUID(as_uuid=True), nullable=False)

    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    # Relationships
    document = relationship("Document", backref="workflow_comments")
    workflow_state = relationship("DocumentWorkflowState", backref="comments")
    assignment = relationship("DocumentWorkflowAssignment", backref="comments")
